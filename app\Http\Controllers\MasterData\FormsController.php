<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Repositories\MasterData\FormRepository;
use App\Traits\ResponseTrait;
use Illuminate\Http\Request;

class FormsController extends Controller
{
    use ResponseTrait;

    private $formRepository;

    public function __construct(FormRepository $formRepository)
    {
        $this->middleware('auth');
        $this->formRepository = $formRepository;
    }

    public function index(Request $request){
        // per page
        $perPage = $request->input('perPage', 10);
        $data = $this->formRepository->getForms($perPage);

        // check if request is ajax
        if($request->ajax()){
            return view('components.pages.master_data.form.datatable', ['data' => $data]);
        }else{
            return view('pages.master_data.form.list', ['data' => $data, 'total_count' => count($this->formRepository->getAllForms()), 'pager' => withPaginate(['searchRouteName' => 'master-data.form.search'])]);
        }
        
    }
}
