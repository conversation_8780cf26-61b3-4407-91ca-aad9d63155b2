<?php

/**
 * Test file to verify REAL USER'S COMPUTER IP logging functionality
 * Run this from your Laravel application to test the IP logging
 * 
 * Usage: 
 * 1. Place this file in your Laravel root directory
 * 2. Run: php artisan tinker
 * 3. Then run: include 'test_real_user_ip.php';
 */

echo "Testing Activity Log with REAL USER'S COMPUTER IP Address...\n\n";

// Test current IP detection
echo "=== CURRENT IP DETECTION TEST ===\n";
echo "Laravel request()->ip(): " . (function_exists('request') ? request()->ip() : 'N/A') . "\n";
echo "Server REMOTE_ADDR: " . ($_SERVER['REMOTE_ADDR'] ?? 'N/A') . "\n";
echo "Server HTTP_X_FORWARDED_FOR: " . ($_SERVER['HTTP_X_FORWARDED_FOR'] ?? 'N/A') . "\n";
echo "Server HTTP_X_REAL_IP: " . ($_SERVER['HTTP_X_REAL_IP'] ?? 'N/A') . "\n";
echo "Server HTTP_CLIENT_IP: " . ($_SERVER['HTTP_CLIENT_IP'] ?? 'N/A') . "\n";

if (function_exists('getRealUserIP')) {
    echo "Real User IP Function: " . getRealUserIP() . "\n";
} else {
    echo "Real User IP Function: NOT LOADED (run this in Laravel context)\n";
}
echo "\n";

// Test 1: Automatic IP logging through trait (when models are saved)
echo "1. Testing automatic REAL USER IP logging through ActivityLogCustom trait:\n";
echo "   - When you create/update/delete any model with the trait, REAL USER IP will be automatically logged\n";
echo "   - Example: User::create(['name' => 'Test User', ...])\n";
echo "   - This will now capture the user's computer IP, not server IP\n\n";

// Test 2: Manual logging with IP using helper function
echo "2. Testing manual logging with REAL USER IP helper function:\n";
echo "   - Use: logActivityWithIP('User logged in', ['user_id' => 123])\n";
echo "   - Use: activityWithIP()->causedBy(auth()->user())->log('Custom action')\n";
echo "   - These now use getRealUserIP() instead of request()->ip()\n\n";

// Test 3: Manual logging with tap method
echo "3. Testing manual logging with tap method (UPDATED):\n";
echo "   - Use: activity()->causedBy(auth()->user())->tap(function(\$activity) {\n";
echo "            \$activity->ip = getRealUserIP(); // CHANGED FROM request()->ip()\n";
echo "          })->log('Custom action with REAL USER IP')\n\n";

// Test 4: How to test real IP
echo "4. How to test REAL USER IP capture:\n";
echo "   - Access your app from another device on the same network\n";
echo "   - Use your computer's network IP (e.g., http://*************/newHR)\n";
echo "   - Deploy to production server\n";
echo "   - The IP should now show the actual user's computer IP, not ::1 or 127.0.0.1\n\n";

// Test 5: Check database structure
echo "5. Verify database structure:\n";
echo "   - Check that 'ip' column exists in activity_log table\n";
echo "   - Run: DESCRIBE activity_log; in your database\n";
echo "   - New entries should show real user IPs instead of localhost\n\n";

echo "=== IMPORTANT NOTES ===\n";
echo "- If you still see ::1 or 127.0.0.1, you're testing on localhost\n";
echo "- To see real IPs, access from different devices or deploy to production\n";
echo "- The function prioritizes headers that contain real client IPs\n";
echo "- This will capture the user's personal computer IP address\n\n";

echo "Test completed! The system now captures REAL USER COMPUTER IPs.\n";
