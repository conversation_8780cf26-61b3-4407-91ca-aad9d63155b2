<?php

use App\libraries\jalalidatetime\JDateTime;
use Morilog\Jalali\Jalalian;
use App\Models\Recruitment\EmployeeExperience;
use App\Models\Recruitment\EmployeeMakafat;
use App\Models\Recruitment\EmployeePromotion;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use App\Traits\ResponseTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Intervention\Image\Facades\Image;
use function PHPSTORM_META\type;

function unixTotime($unixTime)
{
   if ((string)$unixTime != '...') {
      $unixTime = str_pad($unixTime, 6, '0', STR_PAD_LEFT);
      $unixTime = preg_replace('/(\d{2})(\d{2})(\d{2})/', "$1:$2:$3", $unixTime);
      $unixTimeToRealTime = date("g:i a", strtotime($unixTime));
      return $unixTimeToRealTime;
   }
   return '';
}
/**
 * author:m-hanif
 * date:2024-02-12
 * desc: it returns last day of a given shamsi month and year
 */
function getLastDayOfShamsiMonth($year, $month)
{
   //filter month;
   if (!is_numeric($year) || !is_numeric($month) || strlen($year) !== 4 || $month < 1 || $month > 12) {
      return 'Invalid year or month';
   }

   // Format month to ensure it's two digits
   $month = str_pad($month, 2, '0', STR_PAD_LEFT);
   return Jalalian::fromFormat('Y-m-d H:i:s', $year . '-' . $month . '-' . '01' . ' 12:00:01')->getEndDayOfMonth()->getDay();
}


function getSignInImage($images, $date)
{
   try {
      if (!is_null($images) && count($images) > 0) {
         foreach ($images as $img) {
            if (!is_null($img) && strlen($img->path) > 0) {
               if ($date == $img->date) {
                  $path = explode('_', $img->path);
                  $time = (int) substr($path[0], -6); //get the time part in path

                  if ($time <= 120000) {
                     return config('app.att_image_source_ip') . $img->path;
                  } else {
                     return asset('assets/img/avatars/blank_profile.png');
                  }
               }
            }
         }
      }
      return asset('assets/img/avatars/blank_profile.png');
   } catch (\Exception $ex) {
      throw $ex;
   }
}

function getSignOutImage($images, $date)
{
   try {
      $str = '';

      if (!is_null($images) && count($images) > 0) {
         foreach ($images as $img) {
            if (!is_null($img) && strlen($img->path) > 0) {
               $path = explode('_', $img->path);
               $time = (int) substr($path[0], -6); //get the time part in path
               $str = $str . ' - ' . $time;
               if ($date == $img->date) {
                  if ($time > 120000) {
                     return config('app.att_image_source_ip') . $img->path;
                  } else {
                     return asset('assets/img/avatars/blank_profile.png');
                  }
               }
            }
         }
      }
      return asset('assets/img/avatars/blank_profile.png');
   } catch (\Exception $ex) {
      throw $ex;
   }
}

function getEployeeMonthsForAttendanceNotification()
{
   try {
      $currentDate = date('Y-m-d');
      $toDate = $currentDate;
      $currentShamsiDate = getCurrentShamsiDate();
      $currentShamsiDateExploded = explode('-', $currentShamsiDate);
      $fromDate = "";
      if ($currentShamsiDateExploded[1] == 1) // it meas its first mont of shamsi date (hamal)
      {
         $fromDate = ($currentShamsiDateExploded[2] - 1) . "-12" . "-01"; // if the above condation is true so we need to go one moth back and if the month is hamal so we have to go one year back and get the last month of it.
         $fromDate = dateTo('01' . "-12" . '-' . ($currentShamsiDateExploded[2] - 1), 'miladi', false);
      } else {  // so now we need go one month back to the current year 
         $fromDate = $currentShamsiDateExploded[2] . '-' . ($currentShamsiDateExploded[1] - 1) . "-01";
         $fromDateExploded = explode('-', $fromDate);
         $fromDate = dateTo(($fromDateExploded[2] . '-' . $fromDateExploded[1] . '-' . $fromDateExploded[0]), 'miladi', false);
      }
      $Dates = ['fromDate' => $fromDate, 'toDate' => $toDate];
      return $Dates;
   } catch (\Exception $ex) {
      throw $ex;
   }
}

function getDaysOfTheMonth($from, $to)
{

   $begin = new DateTime($from);
   $end = new DateTime($to);
   $interval = DateInterval::createFromDateString('1 day');
   $period = new DatePeriod($begin, $interval, $end);
   $Days = [];
   $counter = 0;
   foreach ($period as $da) {
      array_push($Days, $da->format('Y-m-d'));
      $counter++;
   }
   $Days = ['days' => $Days, 'count' => $counter];
   return $Days;
}

function calculate_attendence_date($day, $month, $year, $type = 'start_date')
{
   $day = (int)$day;
   $month = (int)$month;
   $year = (int)$year;

   if ($type == 'start_date') {
      // if ($month == 1) {
      //    $from = dateToMiladi($year - 1, 12, $day);
      // } else {
      //    $from = dateToMiladi($year, $month - 1, $day); //the start day of attendance month is 11 of prevouce month
      // }
      return dateToMiladi($year, $month, $day);
   } else {
      $to = dateToMiladi($year, $month, $day);
      if ($to > date('Y-m-d')) {
         $to = date('Y-m-d');
      }

      return $to;
   }
}

function dateToMiladi($y, $m, $d)
{
   $y = (int)$y;
   $m = (int)$m;
   $d = (int)$d;
   //$converted = Dateconverter::JalaliToGregorian($y,$m,$d);
   $converted = jdatetime::toGregorian($y, $m, $d);
   $month = $converted[1];
   $day = $converted[2];
   if ($converted[1] < 10) {
      $month = '0' . $converted[1];
   }
   if ($converted[2] < 10) {
      $day = '0' . $converted[2];
   }
   return $converted[0] . '-' . $month . '-' . $day;
}

function att_month_days($type = 0)
{
   if ($type == 0) { //start day
      return 1;
   } else { //end day
      // return 11;
      return 1;
   }
}
/**
 * author:m-hanif
 * date:2024-11-26
 * desc: it returns the  last day of a specific month
 */
function getLastDayOfMonth($year, $month)
{
   if ($month < 1 || $month > 12) {
      return false;
   }
   $date = date('Y-m-t', strtotime("$year-$month-01")); // 't' returns the number of days in the month
   return (int)explode('-', $date)[2];
}

/**
 * Get employee experiences
 * @param id employee id
 */
function getEmployeeExperiences($id)
{
   try {
      if (!is_null($id)) {
         $data = EmployeeExperience::where('employee_id', $id)->orderBy('date_from', 'DESC')->get();
         return $data;
      }
      return [];
   } catch (\Exception $ex) {
      return [];
   }
}


/**
 * Get employee khedmat period
 * @param id employee id
 */
function getEmployeeKhedmatPeriod($id, $return_days = false, $return_arr = false)
{
   try {
      if (!is_null($id)) {
         $data = EmployeeExperience::where('employee_id', $id)->orderBy('id', 'DESC')->get();

         $khedmat_days = 0;

         if (!is_null($data) && count($data) > 0) {
            foreach ($data as $item) {
               $date1 = time();
               $date2 = time();

               if (!is_null($item->date_from)) {
                  $date1 = strtotime($item->date_from);
               }
               if (!is_null($item->date_to)) {
                  $date2 = strtotime($item->date_to);
               }

               $diff = $date2 - $date1;
               $khedmat_days = $khedmat_days + floor($diff / (60 * 60 * 24));
            }
         }

         if ($khedmat_days >= 365) {
            $year = (int)($khedmat_days / 365);
            if ($khedmat_days % 365 >= 30) {
               $month = (int)(($khedmat_days % 365) / 30);
               $day = ($khedmat_days % 365) % 30;
            } else {
               $month = 0;
               $day = $khedmat_days % 365;
            }
         } else {
            $year = 0;
            $month = (int)($khedmat_days / 30);
            $day = $khedmat_days % 30;
         }


         if ($return_days == true) {
            return $khedmat_days;
         } else if ($return_arr == true) {
            return [
               'year' => $year,
               'month' => $month,
               'days' => $day,
            ];
         } else {
            return $year . ' Y - ' . $month . ' M - ' . $day . ' D';
         }
      }
      return '';
   } catch (\Exception $ex) {
      return '';
   }
}

/**
 * Get experience period
 * @param id experience id
 */
function getExperiencePeriod($id)
{
   try {
      if (!is_null($id)) {
         $exp = EmployeeExperience::find($id);

         $khedmat_days = 0;

         if (!is_null($exp)) {
            $date1 = time();
            $date2 = time();

            if (!is_null($exp->date_from)) {
               $date1 = strtotime($exp->date_from);
            }
            if (!is_null($exp->date_to)) {
               $date2 = strtotime($exp->date_to);
            }

            $diff = $date2 - $date1;
            $khedmat_days = $khedmat_days + floor($diff / (60 * 60 * 24));
         }

         if ($khedmat_days >= 365) {
            $year = (int)($khedmat_days / 365);
            if ($khedmat_days % 365 >= 30) {
               $month = (int)(($khedmat_days % 365) / 30);
               $day = ($khedmat_days % 365) % 30;
            } else {
               $month = 0;
               $day = $khedmat_days % 365;
            }
         } else {
            $year = 0;
            $month = (int)($khedmat_days / 30);
            $day = $khedmat_days % 30;
         }
         return $year . ' Y - ' . $month . ' M - ' . $day . ' D';
      }
      return '';
   } catch (\Exception $ex) {
      return '';
   }
}

/**
 * Get employee last makafat
 * @param employee_id
 */
function getEmployeeLastMakafat($employee_id)
{
   try {
      if (!is_null($employee_id)) {
         return EmployeeMakafat::where('employee_id', $employee_id)->orderBy('id', 'DESC')->with('type')->first();
      }
      return null;
   } catch (\Exception $ex) {
      return null;
   }
}

/**
 * Get employee last promotion
 * @param employee_id
 */
function getEmployeeLastPromotion($employee_id)
{
   try {
      if (!is_null($employee_id)) {
         return EmployeePromotion::where('employee_id', $employee_id)->orderBy('id', 'DESC')->first();
      }
      return null;
   } catch (\Exception $ex) {
      return null;
   }
}

/**
 * Get employee first work experience
 * @param id employee id
 */

function getEmployeeFirstExperience($id)
{
   try {
      if (!is_null($id)) {
         return EmployeeExperience::where('employee_id', $id)->orderBy('date_from', 'ASC')->first();
      }
      return null;
   } catch (\Exception $ex) {
      return null;
   }
}

/**
 * author:m-hanif
 * date:2022-05-10
 * desc: it returns string [active] if a given route muches with the given function parameter
 */
function isActive($activeIn = [])
{
   /**
    * @* @param Type string routeName
    */
   // if (Route::currentRouteName() == $routeName) {
   //    return 'active';
   // }
   // return '';

   if (in_array(Route::currentRouteName(), $activeIn)) {
      return 'active';
   }
   return '';
}

/**
 * author:m-hanif
 * date:2022-05-10
 * desc: it returns string [active] if a given route muches with the given function parameter
 */
function isActivePrefix($prefixedRouteName = null)
{
   /**
    * @* @param Type string prefixedRouteName
    */
   $prefixedRouteName = explode('.', $prefixedRouteName);
   if (\Request::is($prefixedRouteName[0] . '/*')) {
      return 'active';
   }
   return '';
}

/**
 * author:m-hanif
 * date:2022-05-08
 * desc: it renders a pagination component 
 */
function withPaginate($options = null)
{
   /**
    * @* @param Type searchRouteName if you need a searchable datatable attach the search route. the  default is null , search input must have css class [pager-search] in order to get the result , if you want to use select2.js with your select element and you need to have pager-search functionality on it add css class ['pager-search-select2] with your select elements, when is there a case which you dont need to raise onchange event on your select and checkbox tags but just want to include it at search event, add ['offChangeEvent'] class to your select element, if you need to raise pager search with a custom button event just call js function paginator() at your event while dom is loaded
    * @* @param Type cssClass if you need a particular css class for your datatable.the default is null
    * @* @param Type ElementID_indexer:default['p'] if you need to have two pagination in single page change ElementID_indexer to any text you wish for your page
    * @* @param Type pagerItemsContainerID (default 'pager') is your html element container where datatable list its data
    * @* @param Type jsCallBack (default 'null') if you wanted to call a javascript function after pager renders pages  you can pass your js function to this param

    */
   $searchRouteName = isset($options['searchRouteName']) ? $options['searchRouteName'] : '/';
   $cssClass = isset($options['cssClass']) ? $options['cssClass'] : '';
   $pagerItemsContainerID = isset($options['pagerItemsContainerID']) ? $options['pagerItemsContainerID'] : 'pager';
   $jsCallBack = isset($options['jsCallBack']) ? $options['jsCallBack'] : '';
   $ElementID_indexer = isset($options['ElementID_indexer']) ? $options['ElementID_indexer'] : 'p';
   $removeJsCallBack = isset($options['removeJsCallBack']) ? $options['removeJsCallBack'] : '';

   return view('components.tools.utilities.datapager.ajax_data_pager', ['searchRouteName' => $searchRouteName, 'cssClass' => $cssClass, 'pagerItemsContainerID' => $pagerItemsContainerID, 'jsCallBack' => $jsCallBack, 'removeJsCallBack' => $removeJsCallBack, 'ElementID_indexer' => $ElementID_indexer])->render();
}
/**
 * author:m-hanif
 * @Date: 2023-06-20 09:33:07
 * @Desc: returns current shamsi year
 */
function getCurrentShamsiYear()
{
   $date = date('Y-m-d');;
   $ConvertedDate = dateTo($date);
   return $ConvertedDate[0];
}

/**
 * author:m-hanif
 * @Date: 2023-09-18 10:33:07
 * @Desc: returns current shamsi month
 */
function getCurrentShamsiMonth()
{

   $date = date('Y-m-d');;
   $ConvertedDate = dateTo($date);
   return $ConvertedDate[1];
}
/**
 * author:m-hanif
 * @Date: 2023-10-31 09:33:07
 * @Desc: returns current miladi year
 */
function getCurrentMiladiYear()
{
   $date = date('Y-m-d');
   $ConvertedDate = explode('-', $date);
   return $ConvertedDate[0];
}
/**
 * author:m-hanif
 * @Date: 2023-11-15 09:33:07
 * @Desc: returns current miladi date
 */
function getCurrentMiladiDate()
{
   return  date('Y-m-d');
}
/**
 * author:m-hanif
 * @Date: 2023-11-23 09:33:07
 * @Desc: returns current shamsi date
 */
function getCurrentShamsiDate($ReturnArray = false)
{
   $date = date('Y-m-d');
   if ($ReturnArray == true) {
      $ConvertedDate = dateTo($date);
      return $ConvertedDate;
   } else {
      $ConvertedDate = dateTo($date, 'shamsi', false);
      return $ConvertedDate;
   }
}

function getCurrentQamariYear()
{
   $date = date('d-m-Y');
   $ConvertedDate = dateTo($date, 'qamari');
   return $ConvertedDate[0];
}


function dateTo($date, $type = 'shamsi', $ReturnArray = true)
{


   /**
    * @* @param Type string date datestring y-m-d
    * @* @param Type string type default shamsi (shamsi)/(miladi)
    */

   try {
      if (is_null($date)) return '';

      $dateArray = explode('-', $date);

      if ($type == 'miladi') {
         $d = (int)$dateArray[0];
         $m = (int)$dateArray[1];
         $y = (int)$dateArray[2];

         $date = JDateTime::toGregorian($y, $m, $d);

         if ($date[1] < 10) {
            $date[1] = '0' . $date[1];
         }
         if ($date[2] < 10) {
            $date[2] = '0' . $date[2];
         }
         if (!$ReturnArray) {
            return (string) ($date[0] . '-' . $date[1] . '-' . $date[2]);
         }
      } elseif ($type == 'shamsi') {

         $y = (int)$dateArray[0];
         $m = (int)$dateArray[1];
         $d = (int)$dateArray[2];

         $date = JDateTime::toJalali($y, $m, $d);
         if ($date[1] < 10) {
            $date[1] = '0' . $date[1];
         }
         if ($date[2] < 10) {
            $date[2] = '0' . $date[2];
         }
         if ($ReturnArray) {
            // return array($date[2], $date[1], (string)$date[0]);
            return $date;
         } else {
            return $date[2] . '-' . $date[1] . '-' . $date[0];
         }
      } elseif ($type == 'qamari') {
         $d = (int)$dateArray[0];
         $m = (int)$dateArray[1];
         $y = (int)$dateArray[2];

         $date = JDateTime::toJalali($y, $m, $d);
         if ($date[1] < 10) {
            $date[1] = '0' . $date[1];
         }
         if ($date[2] < 10) {
            $date[2] = '0' . $date[2];
         }
         if ($ReturnArray) {
            return $date;
         } else {

            return $date[0] . '-' . $date[1] . '-' . $date[2];
         }
      }

      return $date;
   } catch (\Exception $e) {
      return $e;
   }
}

/**
 * author:m-hanif
 * date:2022-06-11
 * desc: create and delete user sission
 */
function userSession($request, $userSession, $options = 'create')
{
   /**
    * @* @param Type name[] sission name/value 
    * @* @param Type options (default 'create ') to create ,get  or delete session 

    */
   try {
      switch ($options) {
         case 'create':

            $request->session()->put($userSession['name'], $userSession['value']);
            return $request->session()->get($userSession['name'], null);
            break;

         case 'delete':

            return $request->session()->pull($userSession['name'], null);
            break;

         case 'get':
            return $request->session()->get($userSession['name'], null);
            break;

         default:
            return 'wrong set';
            break;
      }
   } catch (\Exception $ex) {
      return $ex->getMessage();
   }
}

function isLeapYear($year = null)
{
   $a = 0;
   $b = 1309;
   $c = $year;
   for ($i = 1309; $i <= $c - 4; $i += 4) {
      // اضافه کردن یک دوره سال کبیسه
      $b += 4;
      // اضافه کردن یک دوره برای برسی دوره ۸ ساله
      $a += 1;
      //
      if ($a % 8 == 0) $b++;
   }
   if ($c == $b) {
      return true;
   } else {
      return false;
   }
}
/**
 * author:m-hanif
 * date:2023-11-10
 * desc: it decrypts a given string or array values and escapes the invalid texts and empty values
 * returnWithSameArrayKeys : when an array key value data passes to this function it returns the encrypted data assigned to the same array key
 * includeUnencryptedData : in some cases data passes to this function might have plain text as well so this option returns the plain texts as plain texts
 */
function decrypter($encryptedArray = [], $returnJSON = false, $returnWithSameArrayKeys = false, $includeUnencryptedData = false)
{
   $decrypted = [];
   if (gettype($encryptedArray) == 'array') {
      foreach ($encryptedArray as $key => $encArray) {
         try {
            if ($encArray == 0 || $encArray == null) {
               continue;
            }
            if ($returnWithSameArrayKeys) {
               $decrypted[$key] = decrypt($encArray);
            } else {
               array_push($decrypted, decrypt($encArray));
            }
         } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {

            if ($includeUnencryptedData) {
               if ($returnWithSameArrayKeys) {

                  $decrypted[$key] = $encArray;
               } else {
                  array_push($decrypted, $encArray);
               }
            }

            continue;
         }
      }
   } else {
      try {
         if ($encryptedArray != '0') {

            $decrypted = decrypt($encryptedArray);
         } else {
            $decrypted = $encryptedArray;
         }

         if ($returnJSON) {
            return json_encode($decrypted);
         }

         return $decrypted;
      } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {

         if ($includeUnencryptedData) {
            return $encryptedArray;
         }
         return $e;
      }
   }
   return $decrypted;
}
/**
 * author:m-hanif
 * date:2024-04-09
 * desc: it initialize a given error code to its related path
 */

function initErrorComponentPath($errorCode)
{
   return View::exists('components.error-pages.' . $errorCode) == true ? 'error-pages.' . $errorCode : 'error-pages.404';
}

function thumb($directoryName, $fileName, $yearBased = false)
{
   try {
      $rootPath = config('lists.ROOT_DIRECTORY');
      $relativePath = null;
      $thumbDirectory = '/thumb';
      if ($yearBased) {
         $exploded = explode('_', $fileName);
         $fileYear = $exploded[0];
         $relativePath = $rootPath . '/' . $fileYear . '/' . $directoryName . $thumbDirectory . '/';
         $rootPath = $relativePath . $fileName;
      } else {
         $relativePath = $rootPath . '/' . $directoryName . $thumbDirectory . '/';
         $rootPath = $relativePath . $fileName;
      }
      $rootPath = storage_path('app/public') . '/' . $rootPath;


      if (file_exists($rootPath)) {
         return true;
      } else {
         $rootPath = config('lists.ROOT_DIRECTORY');

         if ($yearBased) {
            $exploded = explode('_', $fileName);
            $fileYear = $exploded[0];
            $relativePath = $rootPath . '/' . $fileYear . '/' . $directoryName;
            $rootPath = $relativePath  . '/' . $fileName;
         } else {
            $relativePath = $rootPath . '/' . $directoryName;
            $rootPath = $relativePath  . '/' . $fileName;
         }
         $rootPath = storage_path('app/public') . '/' . $rootPath;
      }
      if (!file_exists($rootPath)) {
         return 'file not found!';
      }

      $fullThumbDirectory = storage_path('app/public') . '/' . $relativePath . $thumbDirectory;
      $fullThumbDirectory = str_replace('\\', '/', $fullThumbDirectory);

      

      if (!file_exists($fullThumbDirectory)) {
         mkdir($fullThumbDirectory, 0755, true);
      }
      $img = Image::make($rootPath)->resize(300, 300, function ($constraint) {
         $constraint->aspectRatio();
         $constraint->upsize();
      });

      $img->save($fullThumbDirectory . '/' . $fileName);
      return true;
   } catch (\Exception $th) {
      //throw $th;
      return ($th);
   }
}


/** dsc: to get an image base64 format 
 * @param directoryName // file directory name
 * @param fileName // the name of the file with its extension at database table
 * @param yearBased  // if its true the file must start with a year in its name and separated by underscors if its false any naming convention is accepted
 */

function renderBase64($directoryName, $fileName, $yearBased = false, $isThumbnill = false)
{

   try {
      $rootPath = config('lists.ROOT_DIRECTORY');
      if ($fileName) {
         $isThumb = '';
         if ($isThumbnill) {

            thumb($directoryName, $fileName, $yearBased); // check or create thumb
            $isThumb = '/thumb';
         }
         if ($yearBased) {
            $exploded = explode('_', $fileName);
            $fileYear = $exploded[0];

            $rootPath = $rootPath . '/' . $fileYear . '/' . $directoryName . $isThumb . '/' . $fileName;
         } else {
            $rootPath = $rootPath . '/' . $directoryName . $isThumb . '/' . $fileName;
         }
         $rootPath = storage_path('app/public') . '/' . $rootPath;
         if (file_exists($rootPath)) {
            return ('data:image/' . explode('.', $fileName)[1] . ';base64,' . base64_encode(file_get_contents($rootPath)));
         }
      } else {
         return false;
      }
   } catch (\Exception $ex) {
      dd($ex);
   }
}


// renderBase64(config('lists.PROFILE_PICTURE_ATTACHMENT_DIRECTORY'), auth()->user()->employee->photo)
/** 
 * author:m-hanif
 * date:2024-08-26
 * desc: to get card template page design
 */

function initSearchableComponentPath($datacard)
{

   return View::exists('components.pages.card.' . $datacard->cardtype->cardPeriod->year . '.aop_card_print') == true ? 'pages.card.' . $datacard->cardtype->cardPeriod->year . '.aop_card_print' : 'pages.card.empty_card_design';
}
/** 
 * author:m-hanif
 * date:2024-09-15
 * desc: to return user has permission
 */

function canDo($permissions)
{
   return auth()->user()->can($permissions);
}
/** 
 * author:m-hanif
 * date:2024-09-15
 * @param:$with is array of not required param values at the view
 * desc: to return not allowed view
 */

function notAllowed($with = [], Object $request = null)
{
   if ($request != null && $request->ajax() == true) {
      return Response::json([
         'success' => false,
         'message' => __('general.access_not_allowed'),
         'code' => 403,
      ], 403);
   }
   return view('components.layouts.errors', empty($with) == true ? ['title' => __('general.access_not_allowed')] : $with);
}

/** 
 * author:m-hanif
 * date:2024-10-28
 * @param:$with is array of not required param values at the view
 * desc: to return days between a start date and end-date
 */

function getNumberOfDaysByDates($start_date, $end_date)
{
   return  (int)floor((strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24)) + 1;
}

/**
 * Measure the execution time of a callable.
 *
 * Executes the provided callable and records how long the execution takes.
 * The callable will be invoked with the optional $args provided. This method
 * can either return just the measured duration or both the callable result and
 * the duration depending on the $returnResult flag.
 *
 * Behavior details:
 * - Timing uses a high-resolution clock (microseconds) and is converted to the
 *   requested unit via $unit.
 * - If $returnResult is true the return value is an associative array with
 *   keys 'result' and 'duration'.
 * - If $returnResult is false the return value is the duration only.
 * - If the callable throws and $rethrow is true, the exception/error is
 *   rethrown after the measurement. If $rethrow is false the exception is
 *   captured and returned as the 'result' when $returnResult is true, or
 *   rethrown when $returnResult is false.
 *
 * Parameters:
 * @param callable $callback The function or method to execute and measure.
 * @param array|null $args Optional array of arguments to pass to the callable (default: null).
 * @param bool $returnResult Whether to return the callable result along with the duration.
 *                           If true returns array{result:mixed, duration:float}; if false returns float duration only.
 *                           (default: false)
 * @param string $unit Unit for the returned duration. Supported values:
 *                     's'  => seconds (float),
 *                     'ms' => milliseconds (float),
 *                     'us' => microseconds (float).
 *                     (default: 'ms')
 * @param bool $rethrow If true rethrow exceptions/errors thrown by the callable after measuring.
 *                      If false capture the exception and return it as the 'result' when applicable.
 *                      (default: false)
 *
 * Return value:
 * @return float|array If $returnResult is false: float duration in the requested $unit.
 *                     If $returnResult is true: array{
 *                        result: mixed,    // value returned by the callable (or captured Throwable if an error occurred and $rethrow === false)
 *                        duration: float   // measured duration in the requested $unit
 *                     }
 *
 * Exceptions:
 * @throws Throwable If the callable throws and $rethrow is true, the thrown throwable is propagated.
 *
 * Example usage:
 * // Get duration in milliseconds only:
 * $ms = $this->measureExecutionTime(fn() => doWork());
 *
 * // Get both result and duration (in seconds):
 * ['result' => $value, 'duration' => $seconds] = $this->measureExecutionTime([$this, 'compute'], [42], true, 's');
 * 
 *    $start = microtime(true);
 *    $end = microtime(true);
 *  $ttime['excel'] = round($end - $start, 4);
 * 
 */

function measureExecutionTime(callable $callback, ...$args)
{
    $start = microtime(true);

    $result = call_user_func_array($callback, $args);

    $end = microtime(true);
    $duration = $end - $start;

    return [
        'result' => $result,
        'duration_seconds' => round($duration, 4), // rounded to 4 decimal places
    ];
}


// mintenance
//php artisan maintenance:set-time

/**
 * Helper function to log activity with IP address
 * author: m-hanif
 * date: 2024-12-02
 * desc: Creates activity log entry with automatic IP capture
 */
function logActivityWithIP($description, $properties = [], $eventName = 'custom', $logName = 'user activity log')
{
    return activity()
        ->causedBy(auth()->user())
        ->event($eventName)
        ->withProperties($properties)
        ->tap(function ($activity) {
            $activity->ip = request()->ip();
        })
        ->log($description);
}

/**
 * Helper function to get activity logger with IP capture
 * author: m-hanif
 * date: 2024-12-02
 * desc: Returns activity logger instance with IP capture capability
 */
function activityWithIP()
{
    return activity()->tap(function ($activity) {
        $activity->ip = request()->ip();
    });
}
