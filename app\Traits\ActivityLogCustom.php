<?php

namespace App\Traits;

use Illuminate\Support\Facades\App;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Spatie\Activitylog\LogOptions;

/**
 * desc: this trait  is created to add custom config for activity log and it should be used at models along with spati activity log trait
 * author : m-hanif
 * date : 2024-03-08 :9:30 am
 */
trait ActivityLogCustom
{

    public function getActivitylogOptions():LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('user activity log');

    }

    /**
     * Automatically capture IP address for activity logs
     * This method is called by Spatie Activity Log before saving the activity
     */
    public function tapActivity($activity, $eventName)
    {
        // Capture IP address if request is available
        if (request()) {
            $activity->ip = request()->ip();
        }
    }

    /**
      * for creating a custom create event long
      * activity()->causedBy(auth()->user()->id)->event('created')->withProperties(
      *                         [
      *
      *                         'user_id'=>$request['id'],
      *                         'details'=>$userDepts
      *
      *                         ]
      *                         )->log('table=>user_depts');
     */

}
