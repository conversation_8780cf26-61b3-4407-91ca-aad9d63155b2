<?php

namespace App\Traits;

use Illuminate\Support\Facades\App;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Spatie\Activitylog\LogOptions;

/**
 * desc: this trait  is created to add custom config for activity log and it should be used at models along with spati activity log trait
 * author : m-hanif
 * date : 2024-03-08 :9:30 am
 */
trait ActivityLogCustom
{

    public function getActivitylogOptions():LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*'])
        ->useLogName('user activity log');

    }

    /**
     * Automatically capture REAL USER'S COMPUTER IP address for activity logs
     * This method is called by Spatie Activity Log before saving the activity
     */
    public function tapActivity($activity, $eventName)
    {
        // Capture REAL user's computer IP address if request is available
        if (request()) {
            $activity->ip = $this->getRealUserIP();
        }
    }

    /**
     * Get the real user's computer IP address (not server IP)
     * This captures the actual IP of the user's personal computer
     */
    private function getRealUserIP()
    {
        // Priority order for detecting real user's computer IP

        // 1. Check X-Forwarded-For header (most common for real client IP)
        if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            $ip = trim($ips[0]); // First IP is usually the real client IP
            if (filter_var($ip, FILTER_VALIDATE_IP) && $ip !== '::1' && $ip !== '127.0.0.1') {
                return $ip;
            }
        }

        // 2. Check X-Real-IP header (used by Nginx and other proxies)
        if (!empty($_SERVER['HTTP_X_REAL_IP'])) {
            $ip = $_SERVER['HTTP_X_REAL_IP'];
            if (filter_var($ip, FILTER_VALIDATE_IP) && $ip !== '::1' && $ip !== '127.0.0.1') {
                return $ip;
            }
        }

        // 3. Check Client-IP header
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
            if (filter_var($ip, FILTER_VALIDATE_IP) && $ip !== '::1' && $ip !== '127.0.0.1') {
                return $ip;
            }
        }

        // 4. Check other forwarded headers
        $headers = [
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED'
        ];

        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                if (filter_var($ip, FILTER_VALIDATE_IP) && $ip !== '::1' && $ip !== '127.0.0.1') {
                    return $ip;
                }
            }
        }

        // 5. Fallback to REMOTE_ADDR (direct connection)
        $ip = $_SERVER['REMOTE_ADDR'] ?? request()->ip();

        // If still localhost, try to get network IP
        if ($ip === '::1' || $ip === '127.0.0.1') {
            // For development: try to get the actual network IP
            return $this->getNetworkIP() ?: $ip;
        }

        return $ip;
    }

    /**
     * Try to get network IP for development environments
     */
    private function getNetworkIP()
    {
        // This will help in development to get actual network IP
        if (isset($_SERVER['HTTP_HOST'])) {
            $host = $_SERVER['HTTP_HOST'];
            // If accessing via network IP, extract it
            if (preg_match('/^(\d+\.\d+\.\d+\.\d+)/', $host, $matches)) {
                return $matches[1];
            }
        }
        return null;
    }

    /**
      * for creating a custom create event long
      * activity()->causedBy(auth()->user()->id)->event('created')->withProperties(
      *                         [
      *
      *                         'user_id'=>$request['id'],
      *                         'details'=>$userDepts
      *
      *                         ]
      *                         )->log('table=>user_depts');
     */

}
