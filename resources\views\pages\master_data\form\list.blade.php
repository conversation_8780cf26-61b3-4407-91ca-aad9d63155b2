<x-layouts.master title="{{ __('general.forms') }}">
    @pushOnce('content')
        <x-pages.pagesContainer>
            <div class="pb-2">
                <x-pages.master_data.links />
            </div>

            <div class="card">
                <div class="card-header border-bottom">
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex justify-content-start align-items-center">
                                    <x-tools.utilities.datapager.per-page />
                                </div>
                                <div class="d-flex justify-content-center align-items-center">
                                    <button id="show_hide_sidebar_btn" class="btn btn-sm btn-secondary mx-1" type="button">
                                        <span class="tf-icons bx bx-sidebar"></span>
                                    </button>
                                    <a href="{{ route('master-data.job-description-template.create') }}"
                                        class="btn btn-sm btn-primary text-white">
                                        <span class="tf-icons bx bx-plus me-1"></span> {{ __('general.new_form') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12 d-flex">
                            <div class="me-2" style="border: 1px solid #3367983d; width: fit-content;">
                                <div style="padding: 2px 10px; text-align: center; background: #3367983d;">
                                    {{ __('general.total') }}
                                </div>
                                <div style="text-align: center; padding: 2px 0px;">
                                    {{ $total_count }}
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12" id="main_content"
                            style="padding-{{ app()->getLocale() == 'en' ? 'right' : 'left' }}: 0 !important;">
                            <div  id="pager">
                                <x-pages.master_data.form.datatable :data="$data" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="">
                        {!! $pager !!}
                    </div>
                </div>
            </div>
        </x-pages.pagesContainer>
    @endPushOnce

    {{-- JavaScript scripts --}}
    @pushOnce('pscript')
        <script>
            // prevent form submission when pressing enter button
            $(window).keydown(function(event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            $(document).ready(function() {
                $('#show_hide_sidebar_btn').on('click', function() {
                    $("#sidebar").toggle();
                    let isVisible = $("#sidebar").is(":visible");
                    if (isVisible == true) {
                        $("#main_content").removeClass("col-12");
                        $("#main_content").addClass("col-10");
                    } else {
                        $("#main_content").removeClass("col-10");
                        $("#main_content").addClass("col-12");
                    }
                });
            });
        </script>
    @endPushOnce

    {{-- styles --}}
    @pushOnce('pcss')
        <style>
        </style>
    @endPushOnce
</x-layouts.master>
