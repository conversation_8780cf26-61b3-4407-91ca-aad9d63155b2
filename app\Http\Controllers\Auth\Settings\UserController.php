<?php

namespace App\Http\Controllers\Auth\Settings;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use App\Interfaces\Role\RoleInterface as RoleRepository;
use App\Interfaces\Tashkilat\Department\DepartmentInterface as DepartmentRepository;
use App\Repositories\Attendance\Leave\LeaveRepository;
use App\Repositories\Attendance\Machines\StationesRepository;
use App\Repositories\Permission\PermissionRepository;
use App\Repositories\Recruitment\EmployeeRepository;
use App\Repositories\User\UserRepository;
use App\Traits\UserTypeTrait;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    use UserTypeTrait;
    private $AdmineAuthID = 1;
    /**
     * The User repository instance.
     *
     * @var  App\Interfaces\User\UserRepository
     */
    private $userRepository;

    /**
     * Instantiate a new UserController instance.
     */

    /**
     * The employee repository instance.
     *
     * @var  App\Repositories\Recruitment\EmployeeRepository;
     */
    private $employeeRepository;

    /**
     * Display list of user items.
     *
     * @return \Illuminate\Http\Response
     */

    /**
     * The Role repository instance.
     *
     * @var  App\Interfaces\Role\RoleRepository
     */
    private $roleRepository;
    /**
     * The station repository instance.
     *
     * @var  App\Interfaces\Role\RoleRepository
     */
    private $stationRepository;

    /**
     * The Role repository instance.
     *
     * @var  App\Interfaces\Role\RoleRepository
     */
    private $departmentRepository;

    /**
     * The Permission repository instance.
     *
     * @var  App\Interfaces\Permission\PermissionRepository
     */
    private $permissonRepository;
    /**
     * The leave repository instance.
     *
     * @var  use App\Repositories\Attendance\Leave\LeaveRepository;
     */
    private $leaveRepository;

    public function __construct(UserRepository $userRepository, LeaveRepository $leaveRepository, RoleRepository $roleRepository, PermissionRepository $permissonRepository, DepartmentRepository $departmentRepository, StationesRepository $stationesRepository, EmployeeRepository $employeeRepository)
    {
        $this->departmentRepository = $departmentRepository;
        $this->userRepository = $userRepository;
        $this->roleRepository = $roleRepository;
        $this->permissonRepository = $permissonRepository;
        $this->stationRepository = $stationesRepository;
        $this->employeeRepository = $employeeRepository;
        $this->leaveRepository = $leaveRepository;
    }
    public function index(Request $request)
    {

        try {
            if (canDo('user-settings_module')) {
                $perPage = $request->input('perPage', 10);
                $users = $this->userRepository->list($perPage);

                if ($request->ajax()) {

                    return view('components.pages.authentications.users.list', [
                        'users' => $users,
                    ]);
                }

                return view('pages.authentications.users.index', [
                    'users' => $users,
                    'roles' => $this->roleRepository->getRoles(),
                    'parentDeps' => $this->departmentRepository->getDepartmentsRendered('parent', null, '1', true),
                    'pager' => $this->getPager('users.search'),
                    'permissions' => $this->permissonRepository->getPermissions(),
                    'removeParentDefault' => false,
                    'removeChildDefault' => false,
                ]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            dd($ex);
            //throw $ex;
        }
    }
    public function create(Request $request)
    {

        try {
            if (canDo('user-create_account')) {
                return view('pages.authentications.users.create', [
                    'roles' => $this->roleRepository->getRoles(),
                    'parentDeps' => $this->departmentRepository->getDepartmentsRendered('parent', null, '1', true),
                    'pager' => $this->getPager('users.search'),
                    'permissions' => $this->permissonRepository->getPermissions(),
                    'removeParentDefault' => false,
                    'removeChildDefault' => false,
                ]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            dd($ex);
            //throw $ex;
        }
    }

    public function searchUser(Request $request)
    {

        if ($request->permission != 'null') {

            $request['permission'] = decrypt($request->permission);
        }
        return view('components.pages.authentications.users.list', [
            'users' => $this->userRepository->search($request, $request->perPage),
        ])->render();
    }

    private function getPager($searchRouteName)
    {
        return withPaginate(['searchRouteName' => $searchRouteName]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $action)
    {


        if ($action == 'store') {

            return $this->performAction($this->validateRequest($request->all()), $action, $request->all(), null, $request);
        } elseif ($action == 'update') {
            return $this->performAction($this->validateUpdateRequest($request->all()), $action, $request->all(), null, $request);
        }
        return redirect()->back()->with(['error' => __('general.notfound')]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\User  $user
     * 
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, $id)
    {
        try {
            if (canDo('user-edit_account')) {
                $id = decrypt($id);

                $user = $this->userRepository->getUserByID($id);

                $permissionIds = (array)$user->permissions()->pluck('id');
                $permissionIdsToArray = $permissionIds[key($permissionIds)];

                return view('pages.authentications.users.edit', [
                    'user' => $user,
                    'roles' => $this->roleRepository->getRoles(),
                    'canAccessToAllDeps' =>  $this->userRepository->canAccessToAllDeps($id, null, true),
                    'userPermissions' => $permissionIdsToArray,
                    'userDepts' => $this->departmentRepository->getAllUserDepartmentsRendered($id, null, null, 1)
                ]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return  redirect()->back()->with('error', $ex->getMessage());
            //throw $ex;
        }
    }

    private function userDeptDropDownRenderable($authUserAllowedDeptsObject, $forwardUserDeptsIdsArray = [])
    {
        $options = '';

        foreach ($authUserAllowedDeptsObject as $item) {
            $selected = in_array($item->id, $forwardUserDeptsIdsArray) == true ? "selected" : "";
            $options .= '<option value="' . encrypt($item->id) . '"' . $selected .  '>' . $item->name . '</option>';
        }
        return $options;
    }

    /**
     * view specific user
     *
     * @param  \App\Models\User  $user
     * 
     * @return \Illuminate\Http\Response
     */
    public function view(Request $request)
    {

        try {
            if (canDo('user-view_account_info')) {
                $id = decrypt($request->id);

                $user = $this->userRepository->getUserByID($id);

                return view('pages.authentications.users.dataFiles.view-users', [
                    'user' => $user,
                    'canAccessAllDeps' => $this->userRepository->canAccessToAllDeps($id, null, true),
                    'userDepartment' => $this->departmentRepository->getUserDeps($id),
                    'userPermissions' =>  $user->permissions()->pluck('name_' . app()->getLocale())
                ]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            dd($ex);
            //throw $ex;
        }
    }
    /**
     * view specific user
     *
     * @param  \App\Models\User  $user
     * 
     * @return \Illuminate\Http\Response
     */
    public function changeUserStatus(Request $request)
    {

        try {
            if (canDo('user-active_inactive_account')) {
                $id = decrypt($request->id);
                $user = $this->userRepository->changeUserStatus($id);
                if ($user) {
                    return redirect()->back()->with('success', __('general.updated_success'));
                }
                return redirect()->back()->with('error', __('general.something_wrong_happened'));
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            dd($ex);
            //throw $ex;
        }
    }


    /**
     * Validate request fields
     *
     * @param  \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     *
     */
    private function validateRequest($request)
    {

        return Validator::make($request, [
            'email' => 'required|unique:users,email',
            'password' => 'min:8|required|confirmed',

        ]);
    }

    /**
     * Validate request fields for update
     *
     * @param  \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     *
     */
    private function validateUpdateRequest($request)
    {
        $validatoreData = [

            'id' => 'required',
            'email' => 'required|unique:users,email,' . decrypt($request['id']),
        ];
        if ($request['password']) {
            $validatoreData['password'] = 'min:8|required|confirmed';
        }
        return Validator::make($request, $validatoreData);
    }

    /**
     * Perform store and update functions
     *
     * @param \Validator $validator
     * @param \String $methodType
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Role $role
     *
     * @return \Illuminate\Http\Response
     *
     */


    private function performAction($validator, $methodType, $request, $role, $requestOriginal = null)
    {
        try {

            // dd(Department::with(['parentDepartment'])->select('id', 'name_dr', 'name_ps', 'name_en','year','is_parent')->where('id',1)->parentDepartment->id);
            if ($validator->passes()) {
                FacadesDB::beginTransaction();
                $request['id'] = decrypt($request['id']);
                $request['type'] = isset($request['type']) == true ? decrypt($request['type']) : null;

                if ($methodType == 'store') {

                    $status = $this->userRepository->store($request);

                    if (is_a($status, 'App\Models\User')) {
                        if ((isset($request['user_deps']))) {
                            $deps = $this->decryptArray($request['user_deps']);
                            if ($deps == 'all') {
                                $this->userRepository->canAccessToAllDeps($status->id);
                            } else {

                                $depsObject = $this->departmentRepository->getDepartmentByIDs($deps, true);
                                $status =  $this->userRepository->syncUserDepartment($depsObject, $status->id);
                                if ($status) {

                                    activity()->causedBy(auth()->user()->id)->event('created')->withProperties(
                                        [

                                            'dept_attached_to_user_id' => $request['id'],
                                            'details' => $deps

                                        ]
                                    )->tap(function ($activity) {
                                        $activity->ip = getRealUserIP();
                                    })->log('table=>user_depts');
                                }
                            }
                        }

                        if (isset($request['user_stations'])) {
                            activity()->causedBy(auth()->user()->id)->event('created')->withProperties(
                                [

                                    'stations_attached_to_user_id' => $request['id'],
                                    'details' => $request['user_stations']

                                ]
                            )->tap(function ($activity) {
                                $activity->ip = getRealUserIP();
                            })->log('table=>user_depts');
                            $this->userRepository->syncUserAttStations($this->decryptArray($request['user_stations']), $status->id);
                        }
                        FacadesDB::commit();

                        return redirect()->back()->with('success', __('general.created_success'));
                    } else {
                        FacadesDB::rollback();
                        return redirect()->back()->with('error', $status->getMessage());
                    }
                }
                if ($methodType == 'update') {
                    $selectedUser = $this->userRepository->getUserByID($request['id']);
                    $isOriginalSuperAdminRightWithUserTypeStepRight = auth()->user()->id == $this->AdmineAuthID ? true : false;

                    if ($isOriginalSuperAdminRightWithUserTypeStepRight == false) {

                        if ($selectedUser->id != $this->AdmineAuthID && (auth()->user()->type >= $request['type'])) {
                            $isOriginalSuperAdminRightWithUserTypeStepRight = true;
                        }
                    }

                    if (!$isOriginalSuperAdminRightWithUserTypeStepRight) {

                        //  we do not allow a normal user to update a user with special permissions or those user types which its type level is smaller than selectedUser type or original supper admin
                        return notAllowed([], $requestOriginal);
                    }

                    
                    $status = $this->userRepository->update($request);

                    if (!is_a($status, 'Illuminate\Database\QueryException')) {

                        if (isset($request['clear_department']) || !isset($request['user_deps'])) {

                            $this->userRepository->syncUserDepartment([], $request['id']);
                        }



                        if ((isset($request['user_deps']))) {

                            $deps = $this->decryptArray($request['user_deps']);
                            if (isset($request['can_access_all_deps']) && $request['can_access_all_deps'] == 'on') {
                                $this->userRepository->canAccessToAllDeps($request['id'], true);
                                if ($deps && count($deps) > 0) {
                                    $deps = $this->departmentRepository->getDepartmentByIDs($deps, true);
                                    $status =  $this->userRepository->syncUserDepartment($deps, $request['id']);
                                }
                            } else {
                                $this->userRepository->canAccessToAllDeps($request['id'], false); // revoke all dep access 
                                $deps = $this->departmentRepository->getDepartmentByIDs($deps, true);
                                $status =  $this->userRepository->syncUserDepartment($deps, $request['id']);
                            }
                        } else {

                            if (isset($request['can_access_all_deps']) && $request['can_access_all_deps'] == 'on') {

                                $this->userRepository->canAccessToAllDeps($request['id'], true);
                                $this->userRepository->syncUserDepartment([], $request['id']);
                            } else {

                                $this->userRepository->canAccessToAllDeps($request['id'], false); // revoke all dep access 

                            }
                        }

                        $stations = isset($request['user_stations']) != true ? [] : $request['user_stations'];

                        $this->userRepository->syncUserAttStations($this->decryptArray($stations),  $request['id']);
                        FacadesDB::commit();
                        return redirect()->route('authentication.users')->with('success', __('general.updated_success'));
                    } else {
                        FacadesDB::rollback();
                        return redirect()->back()->with('error', $status->getMessage());
                    }
                }
            } else {
                FacadesDB::rollback();
                return
                    redirect()
                    ->back()
                    ->withErrors($validator)
                    ->withInput();
            }
        } catch (\Exception $ex) {
            //throw $th;
            dd($ex);
        }
    }
    private function decryptArray($encryptedArray = [])
    {
        $decrypted = [];

        foreach ($encryptedArray as $encArray) {
            if ($encArray !== 0) {
                $decryptedValue = decrypt($encArray);
                if ($decryptedValue != 'all') {
                    $decrypted[] = $decryptedValue;
                } else {
                    return 'all';
                }
            }
        }

        return $decrypted;
    }
    public function getPaginatedData(Request $request)
    {
        $perPage = $request->input('perPage', 1);

        $users = User::whereNull('deleted_at')->paginate($perPage);

        return view('components.pages.authentications.users.list', ['users' => $users]);
    }

    public function getUserDeps(Request $request)
    {


        try {

            $userType = decrypt($request->userType);
            $userID = null;
            if (isset($request->userID)) {
                $userID = decrypt($request->userID);
            }
            $userType = decrypt($request->userType);

            if ($userType != 1 &&  $request->parentID != 0) { // without normal user
                $rendered = $this->departmentRepository->getDepartmentsRendered($request->type, decrypt($request->parentID), 1, $userID, ['type' => $userType]);

                return  $rendered . $this->filterHRUsers(true, $userID);
            }
            return 'normal';
        } catch (\Exception $e) {

            return $e;
        }
    }
    private function filterHRUsers($rendereable = true, $userID = null)
    {
        $selected = '';
        if ($userID) {
            $selected =   $this->userRepository->getUserByID($userID)->can_access_all_deps == '1' ? 'selected' : '';
        }

        if (auth()->user()->type == self::$systemAdmin && $rendereable) { // system admin
            return '<option ' . $selected . '  value="' . encrypt('all') . '" >' . __("attendance/att.AccessToAllDepartments") . '</option>';
        }
    }
    public function getAllUserDeps(Request $request)
    {
        try {

            $userID = null;
            if (isset($request->userID)) {
                $userID = decrypt($request->userID);
            }
            $userDepartmentParentDepartmentID = $this->userRepository->getUserByID($userID)->employee->department->parentDepartment->id;
            $selectedUserDeps = Arr::flatten($this->userRepository->getUserByID($userID)->userDepartment()->where('type', 1)->pluck('department_id'));

            $userDepts = $this->departmentRepository->getDepartmentsRendered('child', $userDepartmentParentDepartmentID, 1, false);

            $userDepts = $this->userDeptDropDownRenderable($userDepts, $selectedUserDeps);
            return $userDepts .= $this->filterHRUsers(true, $userID);
        } catch (\Exception $e) {
            dd($e);
            return $e;
        }
    }
    public function getAllDepartments(Request $request)
    {
        if (isset($request->userID)) {

            return $this->departmentRepository->getAllUserDepartmentsRendered(decrypt($request->userID), null, null, 1);
        } else {

            return $this->departmentRepository->getDepartmentsRendered(null, null, 1, true);
        }
    }

    public function getAIPUser(Request $request)
    {
        try {
            if (canDo('attendance-save_daily_attendance')) {
                return [
                    'user' => eDecrypt(auth()->user()->email, 'key_hrmis'),
                    'key' => auth()->user()->password,
                    'cn' => config('app.name')
                ];
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            Log::error($ex->getMessage());
            dd($ex);
            //throw $ex;
        }
    }
    public function GetAPIToken(Request $request)
    {
        try {
            if (canDo('attendance-save_daily_attendance')) {

                $isAvailable = userSession($request, ['name' => $request->params['name']], 'get');
                if ($isAvailable) {
                    return $isAvailable;
                }
                return "expired";
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            Log::error($ex->getMessage());
            return Response($ex, 405);
            //throw $ex;
        }
    }
    public function removeAPIToken(Request $request)
    {
        try {
            if (canDo('attendance-save_daily_attendance')) {
                $isAvailable = userSession($request, ['name' => $request->params['name']], 'delete');
                if ($isAvailable) {
                    return $isAvailable;
                }
                return "expired";
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return Response($ex, 405);
            //throw $ex;
        }
    }
    public function getUserStations(Request $request)
    {

        try {
            $userStations = '';
            if (isset($request->user_id)) {
                $userStations = $this->stationRepository->getUserStations(decrypt($request->user_id), true);
            }
            $stations = $this->stationRepository->listsAll(false, false);

            return view('components.pages.attendance.stations.station_select_data', ['userStations' => $userStations, 'stations' => $stations]);
        } catch (\Exception $ex) {
            return $ex->getMessage();
        }
    }
    public function searchEmpAsync(Request $request)
    {

        $isValideForAuth = $this->userRepository->getUserByEmployeeID($request->emp);

        if ($isValideForAuth) {
            return __('general.user_already_has_account');
        }
        return $this->employeeRepository->getEmployeeAttrById($request->emp, ['name', 'last_name', 'father_name', 'current_position_dr']);
        // return view('components.pages.attendance.stations.station_select_data',['userStations'=>$userStations,'stations'=>$stations]);


    }
    public function loadEmployeeDetails(Request $request)
    {

        $emp_data = $this->employeeRepository->getEmployeeAttrById($request->emp, ['name', 'last_name', 'father_name', 'current_position_dr', 'department_id', 'parent_department_id', 'employees.id as emp_id', 'email']);

        if ($emp_data) {
            if (auth()->user()->can_access_all_deps) {
                return view('pages.authentications.users.create_data', [
                    'emp_data' => (array)$emp_data,
                    'parentDeps' => $this->departmentRepository->getDepartmentsRendered('parent', $emp_data->parent_department_id, 1, true),
                    // 'users' => $this->userRepository->list($perPage),
                    'roles' => $this->roleRepository->getRoles(),
                    'permissions' => $this->permissonRepository->getPermissions(),
                    'removeParentDefault' => true,
                    'removeChildDefault' => true,
                ]);
            } elseif ($this->departmentRepository->checkGivenDepOnUserDeps($emp_data->department_id)) {
                return view('pages.authentications.users.create_data', [
                    'emp_data' => (array)$emp_data,
                    'parentDeps' => $this->departmentRepository->getDepartmentsRendered('parent', $emp_data->parent_department_id, 1, true),
                    // 'users' => $this->userRepository->list($perPage),
                    'roles' => $this->roleRepository->getRoles(),
                    'permissions' => $this->permissonRepository->getPermissions(),
                    'removeParentDefault' => true,
                    'removeChildDefault' => true,
                ]);
            }

            return response(__('general.access_not_allowed'), 505);
        } else {
            return response(__('general.notfound', 404));
        }
    }

    /**
     * get all roles of a user
     *
     * @param  \App\Models\User  $userID
     *
     */
    public function getUserRoles($userID)
    {
        return $this->userRepository->getUserRoles(encrypt($userID));
    }

    public function viewUserStatus(Request $request)
    {

        $employeeID = decrypter($request->refEmp);

        if ($employeeID) {
            $data['leaves'] = $this->leaveRepository->getLeaves($employeeID, getCurrentShamsiYear(), null, null, true);
            $data['leaves_totals'] = $this->leaveRepository->getLeavesTotal($data['leaves'], $employeeID, getCurrentShamsiYear());

            return view('components.pages.user.userDataPages.profile.profileDetails', ['leaves_totals' => $data['leaves_totals'], 'showTotal' => false]);
        }


        return ''; //.pages.attendance.attendance.employee_attendance.leaves.leaves_total',['leaves_totals'=>$data['leaves_totals'],'showTotal'=>false]
    }
    public function viewUserChangePass(Request $request)
    {

        $employeeID = $request->refEmp;
        if ($employeeID) {
            return view('components.pages.user.userDataPages.profile.user_change_pass', ['empID' => $employeeID]);
        }

        return '';
    }
    private function validateChangePasss(Request $request)
    {
        // Define the validation rules
        $rules = [
            'password' => 'required|string|min:8|confirmed',
        ];

        // Create the validator
        $validator = Validator::make($request->all(), $rules);

        // Check if validation fails
        if ($validator->fails()) {
            // Return the validation error messages
            return $validator->errors()->first();
        }

        // If validation passes
        return true;
    }
    public function userChangePass(Request $request, $empID)
    {
        try {
            $isValid = $this->validateChangePasss($request);
            if ($isValid === true) {
                $user = $this->userRepository->getUserByEmployeeID(decrypter($empID));
                $isUpdated =   $this->userRepository->userChangePass($user, $request);
                if ($isUpdated) {
                    return redirect()->back()->with('success', __('general.updated_success'));
                }
                return redirect()->back()->with('error', __('general.not_successful'));
            }

            return redirect()->back()->with('error', $isValid);
        } catch (\Exception $ex) {
            return redirect()->back()->with('error', $ex->getMessage());
        }
    }
}
