<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forms', function (Blueprint $table) {
            $table->id();
            $table->string('name_ps');
            $table->string('name_dr')->nullable();
            $table->string('name_en')->nullable();
            $table->string('path', 255);
            $table->tinyInteger('status')->default(1);
            $table->tinyInteger('flag')->default(1)->comment('1:it forms');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forms');
    }
};
