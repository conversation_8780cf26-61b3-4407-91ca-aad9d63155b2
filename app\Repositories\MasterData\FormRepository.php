<?php

namespace App\Repositories\MasterData;

use Illuminate\Support\Facades\DB as FacadesDB;

class FormRepository{
    
    public function getForms($perPage = 10){
        try{
            $query = $this->getFormsQuery();
            $data = $query->paginate($perPage);
            return $data;
        }catch(\Exception $ex){
            throw $ex;
        }
    }

    public function getFormsQuery(){
        try{
            $query = FacadesDB::table('forms')->select('id', 'name_ps', 'name_dr', 'name_en', 'name_' . app()->getLocale() . ' as name', 'status');
            return $query;
        }catch(\Exception $ex){
            throw $ex;
        }
    }
}