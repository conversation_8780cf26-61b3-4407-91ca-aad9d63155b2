<?php

/**
 * Test file to verify IP logging functionality
 * Run this from your Laravel application to test the IP logging
 * 
 * Usage: 
 * 1. Place this file in your Laravel root directory
 * 2. Run: php artisan tinker
 * 3. Then run: include 'test_activity_ip.php';
 */

echo "Testing Activity Log with IP Address...\n\n";

// Test 1: Automatic IP logging through trait (when models are saved)
echo "1. Testing automatic IP logging through ActivityLogCustom trait:\n";
echo "   - When you create/update/delete any model with the trait, IP will be automatically logged\n";
echo "   - Example: User::create(['name' => 'Test User', ...])\n\n";

// Test 2: Manual logging with IP using helper function
echo "2. Testing manual logging with helper function:\n";
echo "   - Use: logActivityWithIP('User logged in', ['user_id' => 123])\n";
echo "   - Use: activityWithIP()->causedBy(auth()->user())->log('Custom action')\n\n";

// Test 3: Manual logging with tap method
echo "3. Testing manual logging with tap method:\n";
echo "   - Use: activity()->causedBy(auth()->user())->tap(function(\$activity) {\n";
echo "            \$activity->ip = request()->ip();\n";
echo "          })->log('Custom action with IP')\n\n";

// Test 4: Check database structure
echo "4. Verify database structure:\n";
echo "   - Check that 'ip' column exists in activity_log table\n";
echo "   - Run: DESCRIBE activity_log; in your database\n\n";

// Test 5: Check configuration
echo "5. Verify configuration:\n";
echo "   - config/activitylog.php should have: 'activity_model' => \\App\\Models\\Activity::class\n";
echo "   - App\\Models\\Activity should extend Spatie\\Activitylog\\Models\\Activity\n\n";

echo "Test completed! Check your activity_log table for IP addresses in new entries.\n";
